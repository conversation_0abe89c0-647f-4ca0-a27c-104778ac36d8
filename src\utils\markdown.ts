import MarkdownIt from 'markdown-it';
// import mk from '@iktakahiro/markdown-it-katex';
// import 'katex/dist/katex.min.css'; // 引入 KaTeX 的 CSS 样式
// 创建并配置 markdown-it 实例
const md = new MarkdownIt({
  html: true, // 允许 HTML 标签
  breaks: true, // 将换行符转换为 <br>
  linkify: true, // 自动将 URL 转换为链接
  typographer: true // 启用一些语言中性的替换和引号美化
});

// // 注册 KaTeX 插件
// md.use(mk, {
//   throwOnError: false, // KaTeX 渲染错误时不抛出异常
//   errorColor: 'red' // 渲染错误时显示的颜色
// });

// 添加简单的缓存机制
const cache = new Map<string, string>();


/**
 * 渲染 Markdown 和 KaTeX 内容
 * @param text Markdown 格式的文本
 * @returns 渲染后的 HTML
 */
export function renderMarkdown(text: string): string {
  if (!text) return ''; // 先对文本进行预处理，去除 KaTeX 公式内侧空格

  if (cache.has(text)) {
    return cache.get(text)!;
  } // 渲染并缓存
  const result = md.render(text); // 使用预处理后的文本进行渲染
  cache.set(text, result);
  return result;
}

