<script setup lang="ts">
import { onMounted, onUpdated, ref, computed } from 'vue';
import { useDrawerDirection } from '@/composables/useDrawerDirection';
import ContentRenderer from './ContentRenderer.vue';
import {highlightCodeBlocks} from '@/utils/highlightCodeBlocks'
const drawerVisible = ref(false);
const note = ref('');

// 使用动态方向
const { drawerDirection } = useDrawerDirection();


// 展示drawer
const showDrawer = (data: any) => {
  drawerVisible.value = true;
  note.value = data;
};

defineExpose({
  showDrawer
});
</script>
<template>
  <div class="noteDrawer">
    <el-drawer v-model="drawerVisible" :direction="drawerDirection" size="740px" class="drawer">
      <template #header>
        <div class="header-text">
          编者笔记
          <div style="margin-top: 5px" class="line"></div>
        </div>
      </template>

      <div class="wrapper NoteDrawer">
        <span v-if="!note">暂无内容</span>
        <span v-else>
          <ContentRenderer :content="highlightCodeBlocks(note)"></ContentRenderer>
        </span>
      </div>
    </el-drawer>
  </div>
</template>
<style scoped>
.header-text {
  color: var(--color-black);
  font-weight: 600;
  /* word-break: break-all; */
  position: relative;
}
.wrapper {
  font-size: 14px;
  color: var(--color-black);
}
.line {
  width: 140%;
  height: 1px;
  background-color: var(--color-boxborder);
  position: absolute;
  bottom: -10px;
  left: -20px;
}
</style>
