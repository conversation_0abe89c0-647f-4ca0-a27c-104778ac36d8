import { ref } from 'vue';
import {renderMarkdown} from '@/utils/markdown';

// 处理论证块
/**
 * 处理证明块列表
 * @param {object[]} proofList 证明块列表
 * @returns {string[]} 证明块html列表
 */
export function handleProof(proofList: any[]): string[] {
  const tempProofList: any[] = [];
  // 遍历每个证明块
  proofList.forEach((block: any) => {
    // 遍历每个证明块中的每个条件
    block.klgProofCondList.forEach((cond: any) => {
      // 生成html
      tempProofList.push(
        `<span blockid="${block.klgProofBlockId}" condid="${cond.klgProofCondId}" sortid="${cond.sort}">${renderMarkdown(cond.cnt)}</span>`
      );
    });
    // 生成html
    tempProofList.push(`<span blockid="${block.klgProofBlockId}">${renderMarkdown(block.conclusion)}</span>`);
  });
  return tempProofList;
}

// 处理习题
export const handleExercise = (exercise: any): string[] => {
  const tempExerciseList = [];
  tempExerciseList.push(
    `<span etype="stem" type="${exercise.type}" style="width: 100%;">${exercise.stem}</span>`
  );
  if (exercise.content) {
    exercise.content.forEach((item) => {
      tempExerciseList.push(
        `<span etype="content" contentid="${item.optionId}">${item.text}</span>`
      );
    });
  }
  tempExerciseList.push(`<span etype="answer">${exercise.answer}</span>`);
  tempExerciseList.push(`<span etype="explanation">${exercise.explanation}</span>`);
  return tempExerciseList;
};

// 把list转化为exercise
export const transferList2Exercise = (list: any[]): any => {
  const tempExercise = ref<{
    type: number;
    stem: string;
    content: any[];
    answer: string;
    explanation: string;
  }>({
    type: 0,
    stem: '',
    content: [],
    answer: '',
    explanation: ''
  });
  const tempList = ref<any[]>([]);
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素
    if (span && span.hasAttribute('etype')) {
      const etype = span.getAttribute('etype');
      if (span.hasAttribute('type')) {
        const type = span.getAttribute('type');
        if (type) {
          tempExercise.value.type = parseInt(type);
        }
      }
      if (etype === 'content') {
        const op = {
          optionId: span.getAttribute('contentid'),
          text: item
        };
        tempList.value.push(op);
      } else {
        tempExercise.value[etype] = item;
      }
    }
  });
  tempExercise.value.content = tempList.value;
  return tempExercise.value;
};

// 把worker的list转化为exercise
export const transferList2ProofList = (list: any[]): any => {
  const tempList = ref<any[]>([]);
  let tempCondList: any[] = [];
  list.forEach((item: any) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(item, 'text/html');
    const span = doc.querySelector('span'); // 获取最外层的 span 元素

    if (span && span.hasAttribute('condid') && span.getAttribute('condid')?.trim()) {
      const sort = span.getAttribute('sortid')?.trim();
      let newBlankCond: any = {
        cnt: item.toString()
      };
      if (sort) {
        newBlankCond.sort = parseInt(sort);
      }
      tempCondList.push(newBlankCond);
    } else {
      const newBlankBlock: any = {
        conclusion: item.toString(),
        klgProofCondList: tempCondList
      };
      tempList.value.push(newBlankBlock);
      tempCondList = [];
    }
  });

  return tempList;
};

/**
 * 将proofContentHTML数组转换为类似proofList的结构化数据
 * @param {string[]} proofContentHTML 经过handleProof处理后的HTML字符串数组
 * @returns {any[]} 类似proofList结构的数据数组
 */
export function convertProofContentToStructure(proofContentHTML: string[]): any[] {
  if (!proofContentHTML || proofContentHTML.length === 0) {
    return [];
  }

  // 第一步：按blockId分组收集条件和结论
  const blockMap: Map<string, {
    conditions: any[],
    conclusion: string | null,
    blockId: string
  }> = new Map();

  // 遍历所有HTML字符串，按blockId分组
  proofContentHTML.forEach((htmlString: string) => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    const span = doc.querySelector('span');

    if (span) {
      const blockId = span.getAttribute('blockid');
      if (!blockId) return; // 跳过没有blockId的元素

      // 确保该blockId在Map中有一个条目
      if (!blockMap.has(blockId)) {
        blockMap.set(blockId, {
          conditions: [],
          conclusion: null,
          blockId
        });
      }

      const blockData = blockMap.get(blockId);
      const condId = span.getAttribute('condid');
      const sortId = span.getAttribute('sortid');

      // 如果有condid属性，说明这是一个条件
      if (condId && condId.trim()) {
        const condition = {
          klgProofCondId: parseInt(condId),
          sort: sortId ? parseInt(sortId) : 2, // 默认为输入条件
          refId: "0", // 默认值
          cnt: htmlString, // 保持原始HTML格式
          klgProofCondOrderNum: blockData?.conditions.length || 0
        };
        blockData?.conditions.push(condition);
      } else {
        // 没有condid属性，说明这是结论
        blockData!.conclusion = htmlString;
      }
    }
  });

  // 第二步：将Map转换为数组结构
  const resultList: any[] = [];
  let orderNum = 1;

  // 按blockId排序（确保顺序一致）
  const sortedEntries = Array.from(blockMap.entries()).sort((a, b) => {
    return parseInt(a[0]) - parseInt(b[0]);
  });

  // 构建最终结果
  sortedEntries.forEach(([blockId, data]) => {
    // 只有同时有条件和结论的块才是有效的
    if (data.conclusion) {
      resultList.push({
        klgProofBlockId: parseInt(blockId),
        klgProofBlockOrderNum: orderNum++,
        conclusion: data.conclusion,
        klgProofCondList: data.conditions,
        isDel: true
      });
    }
  });

  return resultList;
}